[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ssh-key-manager"
version = "1.0.0"
description = "SSH key management tool with automatic OpenSSH setup"
requires-python = ">=3.8"
dependencies = []

[project.optional-dependencies]
dev = ["pytest>=7.0.0", "black>=22.0.0", "flake8>=4.0.0"]

[project.scripts]
ssh-key-manager = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 79
target-version = ['py38']
