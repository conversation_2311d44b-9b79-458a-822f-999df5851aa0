{
    "folders": [
        {
            "name": "py__<PERSON>shKeyManager",
            "path": "."
        }
    ],
    "settings": {
        // Python Configuration
        "python.defaultInterpreterPath": "${workspaceFolder}/ssh_key_manager/.venv/Scripts/python.exe",
        "python.terminal.activateEnvironment": true,
        "python.terminal.activateEnvInCurrentTerminal": true,

        // // Formatting
        // "python.formatting.provider": "black",
        // "python.formatting.blackArgs": ["--line-length=79"],
        // "editor.formatOnSave": false,
        // "python.formatting.autopep8Args": ["--max-line-length=88"],

        // // Linting
        // "python.linting.enabled": true,
        // "python.linting.flake8Enabled": true,
        // "python.linting.pylintEnabled": false,
        // "python.linting.flake8Args": ["--max-line-length=79"],

        // // Testing
        // "python.testing.pytestEnabled": true,
        // "python.testing.unittestEnabled": false,
        // "python.testing.pytestArgs": ["."],

        // Editor Settings
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "files.eol": "\n",
        "files.insertFinalNewline": true,
        "files.trimTrailingWhitespace": true,

        // File Associations
        "files.associations": {
            "*.jinja-*": "jinja",
            "*.jinja2": "jinja",
            "requirements*.txt": "pip-requirements"
        },

        // Exclude patterns
        "files.exclude": {
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/*.pyo": true,
            "**/venv": true,
            "**/.venv": true,
            "**/.git": true,
            "**/*.egg-info": true,
            "**/.pytest_cache": true,
            "**/logs": true,
            "**/*.log": true
        },

        // Search exclude patterns
        "search.exclude": {
            "**/venv": true,
            "**/.venv": true,
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/logs": true,
            "**/.git": true
        },

        // Terminal
        "terminal.integrated.defaultProfile.windows": "PowerShell",
        "terminal.integrated.env.windows": {
            "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
        },

        // IntelliSense
        "python.analysis.autoImportCompletions": true,
        "python.analysis.typeCheckingMode": "basic",
        "python.analysis.autoSearchPaths": true,
        "python.analysis.extraPaths": ["./src"],

        // Git
        "git.ignoreLimitWarning": true,

        // // Workspace specific
        // "workbench.colorTheme": "Default Dark+",
        // "explorer.confirmDelete": false,
        // "explorer.confirmDragAndDrop": false
    },
    "extensions": {
        "recommendations": [
            // "eamodio.gitlens",
            // "ms-python.black-formatter",
            "ms-python.debugpy",
            "ms-python.flake8",
            "ms-python.isort",
            "ms-python.python",
            "ms-vscode.batch",
            "ms-vscode.powershell",
            "ms-vscode.vscode-json",
            // "redhat.vscode-yaml",
            // "wholroyd.jinja",
        ]
    },
    "launch": {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "Python: Current File",
                "type": "python",
                "request": "launch",
                "program": "${file}",
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}/ssh_key_manager",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
                },
                "justMyCode": true
            },
            {
                "name": "Python: Main Module (py__SshKeyManager)",
                "type": "python",
                "request": "launch",
                "program": "${workspaceFolder}/ssh_key_manager/src/main.py",
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}/ssh_key_manager",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
                },
                "justMyCode": true
            },
            // {
            //     "name": "Python: Debug Tests",
            //     "type": "python",
            //     "request": "launch",
            //     "module": "pytest",
            //     "args": ["-v"],
            //     "console": "integratedTerminal",
            //     "cwd": "${workspaceFolder}/ssh_key_manager",
            //     "env": {
            //         "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
            //     },
            //     "justMyCode": false
            // }
        ]
    },
    "tasks": {
        "version": "2.0.0",
        "tasks": [
            {
                "label": "Python: Run Current File",
                "type": "shell",
                "command": "${command:python.interpreterPath}",
                "args": ["-u", "${file}"],
                "group": {
                    "kind": "build",
                    "isDefault": true
                },
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                },
                "options": {
                    "cwd": "${workspaceFolder}/ssh_key_manager",
                    "env": {
                        "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
                    }
                },
                "problemMatcher": []
            },
            {
                "label": "Python: Install Requirements",
                "type": "shell",
                "command": "${command:python.interpreterPath}",
                "args": ["-m", "pip", "install", "-r", "requirements.txt"],
                "group": "build",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                },
                "options": {
                    "cwd": "${workspaceFolder}/ssh_key_manager"
                },
                "problemMatcher": []
            },
            {
                "label": "Python: Run Tests",
                "type": "shell",
                "command": "${command:python.interpreterPath}",
                "args": ["-m", "pytest", "-v"],
                "group": "test",
                "presentation": {
                    "echo": true,
                    "reveal": "always",
                    "focus": false,
                    "panel": "shared"
                },
                "options": {
                    "cwd": "${workspaceFolder}/ssh_key_manager",
                    "env": {
                        "PYTHONPATH": "${workspaceFolder}/ssh_key_manager/src"
                    }
                },
                "problemMatcher": []
            },
            // {
            //     "label": "Python: Format with Black",
            //     "type": "shell",
            //     "command": "C:\Users\<USER>\Desktop\SCRATCH\2025.06.02-kl.09.41--sshkeys\py__SshKeyManager\\venv\\Scripts\\black.exe",
            //     "args": ["--line-length=88", "${workspaceFolder}/ssh_key_manager/src"],
            //     "group": "build",
            //     "presentation": {
            //         "echo": true,
            //         "reveal": "always",
            //         "focus": false,
            //         "panel": "shared"
            //     },
            //     "options": {
            //         "cwd": "${workspaceFolder}/ssh_key_manager"
            //     },
            //     "problemMatcher": []
            // }
        ]
    }
}
