#!/usr/bin/env python3
"""SSH Key Manager - Minimalist SSH key generation and management utility."""

import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path

DEFAULT_ACCOUNT_NAME = "jh.paulsen.wrk"
DEFAULT_EMAIL_DOMAIN = "gmail.com"


def get_hostname():
    return platform.node().split(".")[0]


def get_ssh_dir():
    return Path.home() / ".ssh"


def get_key_path(name=None):
    ssh_dir = get_ssh_dir()
    hostname = get_hostname()
    if not name:
        name = DEFAULT_ACCOUNT_NAME
    key_name = f"{hostname}.{name}.id_rsa"
    return ssh_dir / key_name


def run_command(cmd, input_data=None, use_shell=False):
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, input=input_data, shell=use_shell
        )
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


def copy_to_clipboard(text):
    try:
        if platform.system() == 'Windows':
            cmd = ['clip']
        elif platform.system() == 'Darwin':
            cmd = ['pbcopy']
        else:
            cmd = ['xclip', '-selection', 'clipboard']
        run_command(cmd, text)
        return True
    except Exception:
        return False


def create_key(name=None, email=None, key_type="rsa", key_bits=4096):
    if not name:
        name = DEFAULT_ACCOUNT_NAME
    if not email:
        email = f"{name}@{DEFAULT_EMAIL_DOMAIN}"

    key_path = get_key_path(name)
    ssh_dir = get_ssh_dir()
    ssh_dir.mkdir(exist_ok=True)

    # Check if key already exists and ask user for confirmation
    private_key_path = Path(key_path)
    public_key_path = Path(str(key_path) + ".pub")

    if private_key_path.exists() or public_key_path.exists():
        print(f"\n⚠️  SSH key already exists: {key_path}")
        replace = input("Replace existing key? (y/n) [n]: ").lower().startswith('y')

        if not replace:
            return {
                "success": False,
                "error": "Key generation cancelled by user"
            }

        # Backup existing keys before replacing
        if private_key_path.exists():
            # Find next available backup filename
            backup_private = Path(str(key_path) + ".bak")
            counter = 1
            while backup_private.exists():
                backup_private = Path(f"{str(key_path)}.bak.{counter}")
                counter += 1
            private_key_path.rename(backup_private)
            print(f"Backed up existing private key to: {backup_private}")

        if public_key_path.exists():
            # Find next available backup filename
            backup_public = Path(str(key_path) + ".pub.bak")
            counter = 1
            while backup_public.exists():
                backup_public = Path(f"{str(key_path)}.pub.bak.{counter}")
                counter += 1
            public_key_path.rename(backup_public)
            print(f"Backed up existing public key to: {backup_public}")

    print(f"Generating {key_bits}-bit {key_type} key for {email}...")
    print(f"Key will be saved to: {key_path}")

    # Handle command construction and execution with platform-specific approach
    if platform.system() == "Windows":
        # On Windows, use shell=True and construct command as string
        cmd_str = f'ssh-keygen -o -t {key_type} -b {key_bits} -C "{email}" -f "{key_path}" -N ""'
        result = run_command(cmd_str, use_shell=True)
    else:
        # Unix-like systems can use list format
        cmd = [
            "ssh-keygen", "-o", "-t", key_type, "-b", str(key_bits),
            "-C", email, "-f", str(key_path), "-N", ""
        ]
        result = run_command(cmd)

    if result["success"]:
        return {
            "success": True,
            "key_path": key_path,
            "public_key_path": str(key_path) + ".pub",
            "message": f"Successfully generated key: {key_path}"
        }
    else:
        return {
            "success": False,
            "error": result.get("stderr", "Unknown error during key generation")
        }


def check_windows_openssh():
    if platform.system() != "Windows":
        return {"success": True, "message": "Not Windows - no check needed"}

    if not shutil.which("ssh-agent"):
        return {
            "success": False,
            "error": "OpenSSH Client not found",
            "fix_command": ('Add-WindowsCapability -Online '
                            '-Name OpenSSH.Client~~~~0.0.1.0')
        }

    check_service_cmd = [
        "powershell", "-Command",
        ("Get-Service ssh-agent -ErrorAction SilentlyContinue | "
         "Select-Object Status, StartType")
    ]
    result = run_command(check_service_cmd)

    if not result["success"] or "ssh-agent" not in result["stdout"]:
        return {
            "success": False,
            "error": "SSH Agent service not found",
            "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
        }

    if "Disabled" in result["stdout"]:
        return {
            "success": False,
            "error": "SSH Agent service is disabled",
            "fix_command": 'Set-Service -Name ssh-agent -StartupType Manual'
        }

    return {"success": True, "message": "OpenSSH properly configured"}


def fix_windows_openssh():
    print("Attempting to fix OpenSSH configuration...")

    install_cmd = [
        "powershell", "-Command",
        "Add-WindowsCapability -Online -Name OpenSSH.Client~~~~0.0.1.0"
    ]
    print("Installing OpenSSH Client...")
    result = run_command(install_cmd)

    if not result["success"]:
        return {
            "success": False,
            "error": f"Failed to install OpenSSH Client: {result.get('stderr', 'Unknown error')}"
        }

    enable_cmd = [
        "powershell", "-Command",
        "Set-Service -Name ssh-agent -StartupType Manual"
    ]
    print("Enabling SSH Agent service...")
    result = run_command(enable_cmd)

    if not result["success"]:
        return {
            "success": False,
            "error": f"Failed to enable SSH Agent service: {result.get('stderr', 'Unknown error')}"
        }

    return {
        "success": True,
        "message": "OpenSSH Client installed and SSH Agent service enabled"
    }


def agent_operation(operation, key_path=None):
    if operation == "start":
        if platform.system() == "Windows":
            check_result = check_windows_openssh()
            if not check_result["success"]:
                print(f"⚠️  OpenSSH issue detected: {check_result['error']}")
                print("Attempting to fix automatically...")

                fix_result = fix_windows_openssh()
                if not fix_result["success"]:
                    return {
                        "success": False,
                        "error": f"OpenSSH configuration failed: {fix_result['error']}",
                        "manual_fix": f"Run as Administrator: {check_result.get('fix_command', 'Install OpenSSH')}"
                    }
                print(f"✓ {fix_result['message']}")

            print("Restarting SSH agent on Windows...")
            stop_cmd = ["powershell", "-Command", "Stop-Service ssh-agent -ErrorAction SilentlyContinue"]
            run_command(stop_cmd)

            start_cmd = ["powershell", "-Command", "Start-Service ssh-agent"]
            result = run_command(start_cmd)

            if not result["success"]:
                print("Using ssh-agent directly...")
                result = run_command(["ssh-agent", "-s"])
        else:
            print("Restarting SSH agent...")
            run_command(["ssh-agent", "-k"])
            result = run_command(["ssh-agent", "-s"])

        if result["success"]:
            return {"success": True, "message": "SSH agent started successfully"}
        else:
            return {"success": False, "error": result.get("stderr", "Failed to start SSH agent")}

    elif operation == "list":
        print("Listing keys in SSH agent...")
        result = run_command(["ssh-add", "-l"])

        if result["returncode"] == 0:
            if not result["stdout"].strip():
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": True, "keys": result["stdout"]}
        else:
            if "The agent has no identities" in result["stderr"]:
                return {"success": True, "message": "No keys loaded in agent"}
            return {"success": False, "error": result["stderr"]}

    elif operation == "add":
        if not key_path:
            key_path = get_key_path()

        print(f"Adding key to SSH agent: {key_path}")
        result = run_command(["ssh-add", str(key_path)])

        if result["success"]:
            return {"success": True, "message": f"Added key: {key_path}"}
        else:
            return {"success": False, "error": result.get("stderr", f"Failed to add key: {key_path}")}

    elif operation == "clear":
        print("Clearing all keys from SSH agent...")
        result = run_command(["ssh-add", "-D"])

        if result["success"]:
            return {"success": True, "message": "Cleared all keys from agent"}
        else:
            return {"success": False, "error": result.get("stderr", "Failed to clear keys")}

    return {"success": False, "error": f"Unknown operation: {operation}"}


def manage_ssh_config(operation, host_alias=None, hostname=None, key_name=None):
    """Manage SSH config entries"""
    ssh_config_path = get_ssh_dir() / "config"

    if operation == "add":
        # Create config entry
        key_path = get_key_path(key_name)
        config_entry = f"""
# {host_alias} configuration
Host {host_alias}
    HostName {hostname}
    User git
    IdentityFile {key_path}
    IdentitiesOnly yes

"""

        # Read existing config or create new
        existing_config = ""
        if ssh_config_path.exists():
            try:
                with open(ssh_config_path, 'r') as f:
                    existing_config = f.read()
            except Exception as e:
                return {"success": False, "error": f"Failed to read config: {e}"}

        # Check if host already exists
        if f"Host {host_alias}" in existing_config:
            return {
                "success": False,
                "error": f"Host '{host_alias}' already exists in SSH config"
            }

        # Append new config
        try:
            with open(ssh_config_path, 'w') as f:
                f.write(existing_config + config_entry)

            # Set proper permissions on Windows
            if platform.system() == "Windows":
                try:
                    subprocess.run([
                        "icacls", str(ssh_config_path), "/inheritance:r"
                    ], check=True, capture_output=True)
                    subprocess.run([
                        "icacls", str(ssh_config_path), "/grant:r", f"{os.environ.get('USERNAME')}:(R,W)"
                    ], check=True, capture_output=True)
                    subprocess.run([
                        "icacls", str(ssh_config_path), "/grant:r", "SYSTEM:(F)"
                    ], check=True, capture_output=True)
                except:
                    pass  # Permissions might fail, but config should still work

            return {
                "success": True,
                "message": f"Added SSH config for {host_alias}",
                "usage": f"git clone {host_alias}:username/repository.git"
            }
        except Exception as e:
            return {"success": False, "error": f"Failed to write config: {e}"}

    elif operation == "list":
        if not ssh_config_path.exists():
            return {"success": True, "configs": [], "message": "No SSH config file found"}

        try:
            with open(ssh_config_path, 'r') as f:
                content = f.read()

            # Parse config entries
            configs = []
            lines = content.split('\n')
            current_host = None
            current_config = {}

            for line in lines:
                line = line.strip()
                if line.startswith('Host ') and not line.startswith('Host *'):
                    if current_host:
                        configs.append(current_config)
                    current_host = line.split('Host ')[1]
                    current_config = {"host": current_host}
                elif current_host and line:
                    if line.startswith('HostName '):
                        current_config["hostname"] = line.split('HostName ')[1]
                    elif line.startswith('IdentityFile '):
                        current_config["identity_file"] = line.split('IdentityFile ')[1]

            if current_host:
                configs.append(current_config)

            return {"success": True, "configs": configs}
        except Exception as e:
            return {"success": False, "error": f"Failed to read config: {e}"}

    elif operation == "remove":
        if not ssh_config_path.exists():
            return {"success": False, "error": "No SSH config file found"}

        try:
            with open(ssh_config_path, 'r') as f:
                lines = f.readlines()

            # Remove the specified host block
            new_lines = []
            skip_block = False

            for line in lines:
                if line.strip().startswith(f'Host {host_alias}'):
                    skip_block = True
                    continue
                elif line.strip().startswith('Host ') and skip_block:
                    skip_block = False
                elif line.strip().startswith('#') and f'{host_alias}' in line:
                    continue  # Skip comment lines for this host

                if not skip_block:
                    new_lines.append(line)

            with open(ssh_config_path, 'w') as f:
                f.writelines(new_lines)

            return {"success": True, "message": f"Removed SSH config for {host_alias}"}
        except Exception as e:
            return {"success": False, "error": f"Failed to remove config: {e}"}

    return {"success": False, "error": f"Unknown operation: {operation}"}


def git_operation(operation, param=None, target_dir=None):
    if operation == "test":
        service = param or "gitlab.com"
        print(f"Testing connection to {service}...")
        cmd = ["ssh", "-T", f"git@{service}"]
        result = run_command(cmd)

        success_messages = [
            "successfully authenticated",
            "You've successfully authenticated",
            "Hi ",
            "Welcome to GitLab"
        ]

        if any(msg in result["stderr"] for msg in success_messages):
            return {
                "success": True,
                "message": f"Successfully connected to {service}",
                "details": result["stderr"]
            }
        else:
            return {
                "success": False,
                "message": f"Failed to connect to {service}",
                "details": result.get("stderr", result.get("stdout", "No output"))
            }

    elif operation == "clone":
        if not param:
            return {"success": False, "error": "No repository URL provided"}

        print(f"Cloning repository: {param}")
        cmd = ["git", "clone", param]
        if target_dir:
            cmd.append(target_dir)

        result = run_command(cmd)

        if result["success"]:
            return {"success": True, "message": "Successfully cloned repository"}
        else:
            return {"success": False, "error": result.get("stderr", "Failed to clone repository")}

    elif operation == "remote":
        if not param:
            return {"success": False, "error": "No remote URL provided"}

        print(f"Adding remote origin: {param}")
        cmd = ["git", "remote", "add", "origin", param]
        result = run_command(cmd)

        if result["success"]:
            verify = run_command(["git", "remote", "-v"])
            return {
                "success": True,
                "message": "Remote added successfully",
                "remotes": verify.get("stdout", "")
            }
        else:
            return {"success": False, "error": result.get("stderr", "Failed to add remote")}

    return {"success": False, "error": f"Unknown operation: {operation}"}


def show_key(name=None, copy_to_clipboard_flag=False):
    private_key_path = get_key_path(name)
    key_path = Path(str(private_key_path) + ".pub")

    if not key_path.exists():
        return {
            "success": False,
            "error": f"Public key not found: {key_path}"
        }

    try:
        with open(key_path, 'r') as f:
            key_content = f.read().strip()

        result = {
            "success": True,
            "key": key_content,
            "key_path": key_path
        }

        if copy_to_clipboard_flag:
            if copy_to_clipboard(key_content):
                result["clipboard_msg"] = "Key copied to clipboard"
            else:
                result["clipboard_msg"] = "Failed to copy to clipboard"

        return result

    except Exception as e:
        return {"success": False, "error": f"Failed to read key: {e}"}


def interactive_session():
    while True:
        print("\n===== SSH Key Manager =====")
        print("What would you like to do?")
        print("1. Create new SSH key")
        print("2. Manage SSH agent")
        print("3. Work with Git repository")
        print("4. View/copy existing key")
        print("5. Exit")

        choice = input("\n> ")

        if choice == "1":
            print("\n--- Create SSH Key ---")
            name = input(f"Key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
            default_email = f"{name or DEFAULT_ACCOUNT_NAME}@{DEFAULT_EMAIL_DOMAIN}"
            email = input(f"Email [{default_email}]: ").strip() or None

            result = create_key(name=name, email=email)

            if result["success"]:
                print(f"\n✓ {result['message']}")

                add_result = agent_operation("add", result["key_path"])
                if add_result["success"]:
                    print("✓ Key automatically added to SSH agent")

                # Get the public key and automatically copy to clipboard
                key_result = show_key(name, copy_to_clipboard_flag=True)
                if key_result["success"]:
                    print("\n" + "="*60)
                    print("🔑 YOUR PUBLIC KEY (ready to add to Git services)")
                    print("="*60)
                    print(key_result["key"])
                    print("="*60)

                    # Show clipboard status
                    if key_result.get("clipboard_msg"):
                        if "copied" in key_result["clipboard_msg"].lower():
                            print("✓ Key automatically copied to clipboard!")
                            print("  → Just paste it into GitLab/GitHub")
                        else:
                            print("⚠️  Could not copy to clipboard automatically")
                            print("  → Please copy the key above manually")

                    print("\n📋 Add this key to your Git service:")
                    print("   GitLab: https://gitlab.com/-/profile/keys")
                    print("   GitHub: https://github.com/settings/keys")
            else:
                print(f"\n✗ Error: {result['error']}")

        elif choice == "2":
            print("\n--- SSH Agent Management ---")
            print("1. Start/restart agent")
            print("2. List loaded keys")
            print("3. Add key to agent")
            print("4. Clear all keys")
            print("5. Back")

            agent_choice = input("\n> ")

            if agent_choice == "1":
                result = agent_operation("start")
                status = "✓" if result["success"] else "✗"
                msg = result.get("message", result.get("error", ""))
                print(f"\n{status} {msg}")

            elif agent_choice == "2":
                result = agent_operation("list")
                print("\nLoaded keys:")
                if "keys" in result:
                    print(result["keys"])
                else:
                    print(result.get("message", "No keys loaded or error checking"))

            elif agent_choice == "3":
                name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
                key_path = get_key_path(name)
                result = agent_operation("add", key_path)
                status = "✓" if result["success"] else "✗"
                msg = result.get("message", result.get("error", ""))
                print(f"\n{status} {msg}")

            elif agent_choice == "4":
                result = agent_operation("clear")
                status = "✓" if result["success"] else "✗"
                msg = result.get("message", result.get("error", ""))
                print(f"\n{status} {msg}")

        elif choice == "3":
            print("\n--- Git Operations ---")
            print("1. Test SSH connection")
            print("2. Clone repository")
            print("3. Add remote origin")
            print("4. Manage SSH config")
            print("5. Back")

            git_choice = input("\n> ")

            if git_choice == "1":
                service = input("Enter Git service [gitlab.com]: ").strip() or "gitlab.com"
                result = git_operation("test", service)
                status = "✓" if result["success"] else "✗"
                msg = result.get("message", result.get("error", ""))
                print(f"\n{status} {msg}")
                if "details" in result:
                    print(f"Details: {result['details']}")

            elif git_choice == "2":
                repo_url = input("Enter repository URL: ").strip()
                target_dir = input("Enter target directory [default]: ").strip() or None
                if repo_url:
                    result = git_operation("clone", repo_url, target_dir)
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
                else:
                    print("\n✗ No repository URL provided")

            elif git_choice == "3":
                remote_url = input("Enter remote URL: ").strip()
                if remote_url:
                    result = git_operation("remote", remote_url)
                    status = "✓" if result["success"] else "✗"
                    msg = result.get("message", result.get("error", ""))
                    print(f"\n{status} {msg}")
                    if result["success"] and "remotes" in result:
                        print("\nConfigured remotes:")
                        print(result["remotes"])
                else:
                    print("\n✗ No remote URL provided")

            elif git_choice == "4":
                print("\n--- SSH Config Management ---")
                print("1. Add SSH config entry")
                print("2. List SSH config entries")
                print("3. Remove SSH config entry")
                print("4. Back")

                config_choice = input("\n> ")

                if config_choice == "1":
                    print("\n--- Add SSH Config Entry ---")
                    host_alias = input("Host alias (e.g., gitlab-work): ").strip()
                    hostname = input("Hostname [gitlab.com]: ").strip() or "gitlab.com"
                    key_name = input(f"Key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None

                    if host_alias:
                        result = manage_ssh_config("add", host_alias, hostname, key_name)
                        status = "✓" if result["success"] else "✗"
                        msg = result.get("message", result.get("error", ""))
                        print(f"\n{status} {msg}")

                        if result["success"] and "usage" in result:
                            print(f"\n📋 Usage example:")
                            print(f"   {result['usage']}")
                            print(f"\n💡 For your repository:")
                            print(f"   git clone {host_alias}:j.workflow/git_work_jorn.git")
                    else:
                        print("\n✗ Host alias is required")

                elif config_choice == "2":
                    result = manage_ssh_config("list")
                    if result["success"]:
                        configs = result.get("configs", [])
                        if configs:
                            print("\n--- SSH Config Entries ---")
                            for config in configs:
                                print(f"Host: {config['host']}")
                                if 'hostname' in config:
                                    print(f"  HostName: {config['hostname']}")
                                if 'identity_file' in config:
                                    print(f"  IdentityFile: {config['identity_file']}")
                                print()
                        else:
                            print(result.get("message", "No SSH config entries found"))
                    else:
                        print(f"\n✗ Error: {result['error']}")

                elif config_choice == "3":
                    host_alias = input("Host alias to remove: ").strip()
                    if host_alias:
                        result = manage_ssh_config("remove", host_alias)
                        status = "✓" if result["success"] else "✗"
                        msg = result.get("message", result.get("error", ""))
                        print(f"\n{status} {msg}")
                    else:
                        print("\n✗ Host alias is required")

        elif choice == "4":
            print("\n--- View/Copy SSH Key ---")
            name = input(f"Enter key name [{DEFAULT_ACCOUNT_NAME}]: ").strip() or None
            copy = input("Copy to clipboard? (y/n) [n]: ").lower().startswith("y")

            result = show_key(name, copy)

            if result["success"]:
                print(f"\nPublic key ({result['key_path']}):")
                print(result["key"])

                if result.get("clipboard_msg"):
                    print(f"\n✓ {result['clipboard_msg']}")

                print("\nAdd this key to your Git service:")
                print("GitLab: https://gitlab.com/-/profile/keys")
                print("GitHub: https://github.com/settings/keys")
            else:
                print(f"\n✗ Error: {result['error']}")

        elif choice == "5":
            print("\nGoodbye!")
            break

        else:
            print("\n✗ Invalid choice. Please try again.")


def main():
    """Main entry point for the SSH Key Manager."""
    if not shutil.which("ssh-keygen"):
        print("ERROR: ssh-keygen not found. Please install SSH.")
        sys.exit(1)

    if len(sys.argv) == 1:
        interactive_session()
    else:
        # Command line mode would go here
        print("Command line mode not implemented in this simplified version")


if __name__ == "__main__":
    main()
