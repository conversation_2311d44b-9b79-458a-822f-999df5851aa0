@echo off
:: SSH Key Manager - Batch launcher
:: Activates virtual environment if present and runs main.py

SETLOCAL
CD /D "%~dp0.." :: Navigate to project root (one level up from src)

SET VENV_PATH=.\.venv

IF EXIST "%VENV_PATH%\Scripts\activate.bat" (
    ECHO Activating virtual environment from %VENV_PATH%...
    CALL "%VENV_PATH%\Scripts\activate.bat"
) ELSE (
    ECHO Virtual environment not found at %VENV_PATH%. Running with system Python.
)

ECHO Launching SSH Key Manager...
python "%~dp0\main.py" %*

ENDLOCAL
